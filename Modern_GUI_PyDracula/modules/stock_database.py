#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据库管理模块
专门用于存储和管理股票相关数据，包括用户查询的股票代码历史记录
"""

import os
import sqlite3
import logging
from datetime import datetime
from typing import Optional, Dict, List

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class StockDatabase:
    """股票数据库管理类"""
    
    def __init__(self, db_path: str = None):
        """
        初始化股票数据库

        Args:
            db_path: 数据库文件路径
        """
        if db_path is None:
            # 获取当前模块所在目录的父目录（Modern_GUI_PyDracula）
            current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            db_path = os.path.join(current_dir, "data", "stock_data.db")

        self.db_path = db_path

        # 确保数据库目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # 初始化数据库
        self.init_database()
        
        logger.info(f"股票数据库初始化完成: {self.db_path}")
    
    def init_database(self):
        """初始化数据库，创建必要的表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建股票查询历史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS stock_query_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    stock_name TEXT,
                    query_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                    is_current BOOLEAN DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建股票基本信息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS stock_basic_info (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT UNIQUE NOT NULL,
                    stock_name TEXT,
                    market TEXT,
                    industry TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建配置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS stock_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT UNIQUE NOT NULL,
                    value TEXT NOT NULL,
                    description TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_code ON stock_query_history(stock_code)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_is_current ON stock_query_history(is_current)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_query_time ON stock_query_history(query_time)')
            
            conn.commit()
            conn.close()
            
            # 不再自动设置默认股票代码，让用户自己选择
            
            logger.info("股票数据库表创建成功")
            
        except Exception as e:
            logger.error(f"股票数据库初始化失败: {e}")
            raise
    
    def set_default_stock_if_not_exists(self):
        """如果没有当前股票代码，设置默认股票代码为600895"""
        try:
            current_stock = self.get_current_stock_code()
            if current_stock is None:
                logger.info("未找到当前股票代码，设置默认股票代码: 600895")
                self.save_stock_query("600895", "张江高科", is_current=True)
        except Exception as e:
            logger.error(f"设置默认股票代码失败: {e}")
    
    def save_stock_query(self, stock_code: str, stock_name: str = None, is_current: bool = True) -> bool:
        """
        保存股票查询记录
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称（可选）
            is_current: 是否为当前查询的股票
            
        Returns:
            bool: 保存是否成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 如果设置为当前股票，先将其他股票的is_current设为False
            if is_current:
                cursor.execute('UPDATE stock_query_history SET is_current = 0')
            
            # 插入新记录
            cursor.execute('''
                INSERT INTO stock_query_history 
                (stock_code, stock_name, is_current, query_time)
                VALUES (?, ?, ?, ?)
            ''', (
                stock_code,
                stock_name,
                1 if is_current else 0,
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))
            
            conn.commit()
            rows_affected = cursor.rowcount
            conn.close()
            
            if rows_affected > 0:
                logger.info(f"股票查询记录保存成功: {stock_code} ({stock_name})")
                return True
            else:
                logger.warning(f"股票查询记录保存失败: {stock_code}")
                return False
                
        except Exception as e:
            logger.error(f"保存股票查询记录失败: {e}")
            return False
    
    def get_current_stock_code(self) -> Optional[str]:
        """
        获取当前股票代码
        
        Returns:
            str: 当前股票代码，如果没有则返回None
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT stock_code FROM stock_query_history 
                WHERE is_current = 1 
                ORDER BY query_time DESC 
                LIMIT 1
            ''')
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                stock_code = result[0]
                logger.info(f"获取当前股票代码: {stock_code}")
                return stock_code
            else:
                logger.info("未找到当前股票代码")
                return None
                
        except Exception as e:
            logger.error(f"获取当前股票代码失败: {e}")
            return None
    
    def get_current_stock_info(self, news_analyzer=None) -> Optional[Dict]:
        """
        获取当前股票信息

        Args:
            news_analyzer: 新闻分析器实例，用于获取股票名称映射

        Returns:
            dict: 包含股票代码和名称的字典，如果没有则返回None
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT stock_code, stock_name, query_time FROM stock_query_history
                WHERE is_current = 1
                ORDER BY query_time DESC
                LIMIT 1
            ''')

            result = cursor.fetchone()
            conn.close()

            if result:
                stock_code = result[0]
                stock_name = result[1] if result[1] else ''

                # 如果数据库中没有股票名称，尝试通过股票代码查找
                if not stock_name and news_analyzer:
                    stock_name = self._get_stock_name_by_code(stock_code, news_analyzer)
                    if stock_name:
                        # 更新数据库中的股票名称
                        self._update_stock_name(stock_code, stock_name)
                        logger.info(f"已更新数据库中的股票名称: {stock_code} -> {stock_name}")

                stock_info = {
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'query_time': result[2]
                }
                logger.info(f"获取当前股票信息: {stock_info}")
                return stock_info
            else:
                logger.info("未找到当前股票信息")
                return None

        except Exception as e:
            logger.error(f"获取当前股票信息失败: {e}")
            return None

    def _get_stock_name_by_code(self, stock_code: str, news_analyzer) -> Optional[str]:
        """
        通过股票代码查找股票名称

        Args:
            stock_code: 股票代码
            news_analyzer: 新闻分析器实例

        Returns:
            str: 股票名称，如果找不到则返回None
        """
        try:
            if not hasattr(news_analyzer, 'stock_codes') or not hasattr(news_analyzer, 'stock_names'):
                logger.warning("新闻分析器中没有股票数据")
                return None

            if not news_analyzer.stock_codes or not news_analyzer.stock_names:
                logger.warning("新闻分析器中的股票数据为空")
                return None

            if stock_code in news_analyzer.stock_codes:
                index = news_analyzer.stock_codes.index(stock_code)
                if index < len(news_analyzer.stock_names):
                    stock_name = news_analyzer.stock_names[index]
                    logger.info(f"通过股票代码找到股票名称: {stock_code} -> {stock_name}")
                    return stock_name

            logger.warning(f"未找到股票代码 {stock_code} 对应的股票名称")
            return None

        except Exception as e:
            logger.error(f"通过股票代码查找股票名称失败: {e}")
            return None

    def _update_stock_name(self, stock_code: str, stock_name: str) -> bool:
        """
        更新数据库中的股票名称

        Args:
            stock_code: 股票代码
            stock_name: 股票名称

        Returns:
            bool: 更新是否成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE stock_query_history
                SET stock_name = ?, updated_at = ?
                WHERE stock_code = ? AND is_current = 1
            ''', (stock_name, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), stock_code))

            conn.commit()
            rows_affected = cursor.rowcount
            conn.close()

            if rows_affected > 0:
                logger.info(f"股票名称更新成功: {stock_code} -> {stock_name}")
                return True
            else:
                logger.warning(f"股票名称更新失败，未找到对应记录: {stock_code}")
                return False

        except Exception as e:
            logger.error(f"更新股票名称失败: {e}")
            return False

    def get_query_history(self, limit: int = 10) -> List[Dict]:
        """
        获取股票查询历史记录
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            list: 查询历史记录列表
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT stock_code, stock_name, query_time, is_current 
                FROM stock_query_history 
                ORDER BY query_time DESC 
                LIMIT ?
            ''', (limit,))
            
            rows = cursor.fetchall()
            conn.close()
            
            history_list = []
            for row in rows:
                history_item = {
                    'stock_code': row[0],
                    'stock_name': row[1] if row[1] else '',
                    'query_time': row[2],
                    'is_current': bool(row[3])
                }
                history_list.append(history_item)
            
            logger.info(f"获取股票查询历史: {len(history_list)} 条记录")
            return history_list
            
        except Exception as e:
            logger.error(f"获取股票查询历史失败: {e}")
            return []
    
    def save_stock_basic_info(self, stock_code: str, stock_name: str, market: str = None, industry: str = None) -> bool:
        """
        保存股票基本信息
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            market: 市场（上海/深圳）
            industry: 行业
            
        Returns:
            bool: 保存是否成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO stock_basic_info 
                (stock_code, stock_name, market, industry, updated_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                stock_code,
                stock_name,
                market,
                industry,
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))
            
            conn.commit()
            rows_affected = cursor.rowcount
            conn.close()
            
            if rows_affected > 0:
                logger.info(f"股票基本信息保存成功: {stock_code} - {stock_name}")
                return True
            else:
                logger.warning(f"股票基本信息保存失败: {stock_code}")
                return False
                
        except Exception as e:
            logger.error(f"保存股票基本信息失败: {e}")
            return False
    
    def get_stock_basic_info(self, stock_code: str) -> Optional[Dict]:
        """
        获取股票基本信息
        
        Args:
            stock_code: 股票代码
            
        Returns:
            dict: 股票基本信息，如果没有则返回None
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT stock_code, stock_name, market, industry, updated_at 
                FROM stock_basic_info 
                WHERE stock_code = ?
            ''', (stock_code,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                stock_info = {
                    'stock_code': result[0],
                    'stock_name': result[1],
                    'market': result[2],
                    'industry': result[3],
                    'updated_at': result[4]
                }
                return stock_info
            else:
                return None
                
        except Exception as e:
            logger.error(f"获取股票基本信息失败: {e}")
            return None
    
    def set_config(self, key: str, value: str, description: str = None) -> bool:
        """
        设置配置项
        
        Args:
            key: 配置键
            value: 配置值
            description: 配置描述
            
        Returns:
            bool: 设置是否成功
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO stock_config 
                (key, value, description, updated_at)
                VALUES (?, ?, ?, ?)
            ''', (
                key,
                value,
                description,
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))
            
            conn.commit()
            rows_affected = cursor.rowcount
            conn.close()
            
            return rows_affected > 0
            
        except Exception as e:
            logger.error(f"设置配置项失败: {e}")
            return False
    
    def get_config(self, key: str) -> Optional[str]:
        """
        获取配置项
        
        Args:
            key: 配置键
            
        Returns:
            str: 配置值，如果没有则返回None
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT value FROM stock_config WHERE key = ?', (key,))
            result = cursor.fetchone()
            conn.close()
            
            return result[0] if result else None
            
        except Exception as e:
            logger.error(f"获取配置项失败: {e}")
            return None
    
    def clear_old_history(self, days: int = 30) -> int:
        """
        清理旧的查询历史记录
        
        Args:
            days: 保留天数
            
        Returns:
            int: 删除的记录数量
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 删除指定天数之前的记录（但保留当前股票记录）
            cursor.execute('''
                DELETE FROM stock_query_history 
                WHERE query_time < datetime('now', '-{} days') 
                AND is_current = 0
            '''.format(days))
            
            conn.commit()
            deleted_count = cursor.rowcount
            conn.close()
            
            if deleted_count > 0:
                logger.info(f"清理了 {deleted_count} 条旧的查询历史记录")
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"清理旧查询历史失败: {e}")
            return 0
